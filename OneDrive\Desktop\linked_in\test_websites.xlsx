import pandas as pd

# Create test data with real company websites
test_data = {
    'Website URL': [
        'https://www.microsoft.com',
        'https://www.google.com', 
        'https://www.apple.com',
        'https://www.amazon.com',
        'https://www.tesla.com',
        'https://www.netflix.com',
        'https://www.adobe.com',
        'https://www.salesforce.com',
        'https://www.oracle.com',
        'https://www.ibm.com'
    ]
}

df = pd.DataFrame(test_data)
df.to_excel(r'C:\Users\<USER>\Downloads\test_websites.xlsx', index=False)
print("✅ Created test file with real company websites!")
print("📁 Saved to: C:\\Users\\<USER>\\Downloads\\test_websites.xlsx")
