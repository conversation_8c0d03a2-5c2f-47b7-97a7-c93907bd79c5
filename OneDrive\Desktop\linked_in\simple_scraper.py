import pandas as pd
import re
import phonenumbers
from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright
import logging
import random
import time

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

INPUT_FILE = r"C:\Users\<USER>\Downloads\website_urls_output.xlsx"
OUTPUT_FILE = r"C:\Users\<USER>\Downloads\website_scraped_data.xlsx"

def validate_email(email):
    """Validate email format."""
    email_regex = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
    if not re.match(email_regex, email, re.IGNORECASE):
        return False
    
    # Skip obvious non-emails
    invalid_patterns = [
        r'\.(png|jpg|jpeg|gif|svg|pdf|css|js|ico)$',
        r'@(example|test|invalid|localhost|domain)\.',
        r'@.*\.(png|jpg|jpeg|gif|svg|pdf)$',
    ]
    
    for pattern in invalid_patterns:
        if re.search(pattern, email, re.IGNORECASE):
            return False
    
    return True

def extract_emails(text):
    """Extract emails from text."""
    email_patterns = [
        r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}",
        r"[a-zA-Z0-9._%+-]+\[at\][a-zA-Z0-9.-]+\[dot\][a-zA-Z]{2,}",
        r"[a-zA-Z0-9._%+-]+\s*\(at\)\s*[a-zA-Z0-9.-]+\s*\(dot\)\s*[a-zA-Z]{2,}",
    ]
    
    emails = set()
    for pattern in email_patterns:
        found_emails = re.findall(pattern, text, re.IGNORECASE)
        for email in found_emails:
            clean_email = email.replace('[at]', '@').replace('[dot]', '.').replace('(at)', '@').replace('(dot)', '.')
            clean_email = re.sub(r'\s+', '', clean_email)
            emails.add(clean_email.lower())
    
    valid_emails = [email for email in emails if validate_email(email)]
    return sorted(valid_emails)

def validate_phone(phone):
    """Validate phone number."""
    try:
        regions_to_try = ['US', 'GB', 'IN', 'CA', 'AU', None]
        
        for reg in regions_to_try:
            try:
                parsed = phonenumbers.parse(phone, reg)
                if phonenumbers.is_valid_number(parsed) or phonenumbers.is_possible_number(parsed):
                    return phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
            except phonenumbers.NumberParseException:
                continue
        return None
    except Exception:
        return None

def extract_phones(text):
    """Extract phone numbers from text."""
    phone_patterns = [
        r"(\+?\d{1,4}[\s.-]?\(?\d{3,4}\)?[\s.-]?\d{3,4}[\s.-]?\d{3,4})",
        r"(\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4})",
        r"(\+?\d{1,4}[\s.-]?\d{2,4}[\s.-]?\d{2,4}[\s.-]?\d{2,4}[\s.-]?\d{2,4})",
        r"(\d{3}[\s.-]?\d{3}[\s.-]?\d{4})",
        r"(\+?\d{10,15})",
        r"(\d{10})",
    ]
    
    potential_phones = set()
    for pattern in phone_patterns:
        found_phones = re.findall(pattern, text)
        potential_phones.update(found_phones)
    
    valid_phones = []
    for phone in potential_phones:
        cleaned_phone = re.sub(r'[^\d+]', '', phone)
        
        if len(cleaned_phone) < 7 or len(cleaned_phone) > 15:
            continue
            
        if re.match(r'^(0+|1+|2+|3+|4+|5+|6+|7+|8+|9+)$', cleaned_phone):
            continue
            
        formatted_phone = validate_phone(cleaned_phone)
        if formatted_phone:
            valid_phones.append(formatted_phone)
        elif len(cleaned_phone) >= 10:
            valid_phones.append(cleaned_phone)
    
    return sorted(list(set(valid_phones)))

def normalize_url(url):
    """Add https:// if missing."""
    if not url or url.strip().lower() in ['n/a', 'na', '', 'none', 'null']:
        return None
    
    url = url.strip()
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    return url.rstrip('/')

def scrape_website(url):
    """Scrape emails and phones from a website."""
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            page = context.new_page()
            page.set_default_timeout(30000)  # 30 seconds
            
            # Go to main page
            page.goto(url, wait_until='domcontentloaded', timeout=30000)
            page.wait_for_load_state('networkidle', timeout=10000)
            
            html = page.content()
            soup = BeautifulSoup(html, 'lxml')
            
            # Extract text from all relevant elements
            text_elements = soup.find_all([
                'p', 'div', 'span', 'a', 'li', 'section', 'footer', 'header', 
                'address', 'td', 'th', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
            ])
            
            text = ' '.join(tag.get_text(separator=' ', strip=True) for tag in text_elements if tag.get_text(strip=True))
            
            # Extract from mailto and tel links
            all_emails = set()
            all_phones = set()
            
            mailto_links = soup.find_all('a', href=re.compile(r'^mailto:', re.IGNORECASE))
            tel_links = soup.find_all('a', href=re.compile(r'^tel:', re.IGNORECASE))
            
            for link in mailto_links:
                href = link.get('href', '')
                email = href.replace('mailto:', '').split('?')[0]
                if validate_email(email):
                    all_emails.add(email.lower())
            
            for link in tel_links:
                href = link.get('href', '')
                phone = href.replace('tel:', '').replace('+', '').replace('-', '').replace(' ', '')
                if len(phone) >= 7:
                    all_phones.add(phone)
            
            # Extract from text
            page_emails = extract_emails(text)
            page_phones = extract_phones(text)
            all_emails.update(email.lower() for email in page_emails)
            all_phones.update(page_phones)
            
            # Try contact page
            contact_links = soup.find_all('a', href=re.compile(r'contact|about', re.IGNORECASE))
            for link in contact_links[:2]:  # Only try first 2 contact links
                href = link.get('href', '')
                if href and not href.startswith('#'):
                    try:
                        if href.startswith('/'):
                            contact_url = url + href
                        elif href.startswith('http'):
                            contact_url = href
                        else:
                            contact_url = url + '/' + href
                        
                        page.goto(contact_url, wait_until='domcontentloaded', timeout=15000)
                        contact_html = page.content()
                        contact_soup = BeautifulSoup(contact_html, 'lxml')
                        
                        contact_text_elements = contact_soup.find_all(['p', 'div', 'span', 'a', 'address'])
                        contact_text = ' '.join(tag.get_text(separator=' ', strip=True) 
                                              for tag in contact_text_elements if tag.get_text(strip=True))
                        
                        contact_emails = extract_emails(contact_text)
                        contact_phones = extract_phones(contact_text)
                        all_emails.update(email.lower() for email in contact_emails)
                        all_phones.update(contact_phones)
                        
                    except Exception:
                        continue
            
            browser.close()
            return sorted(list(all_emails)), sorted(list(all_phones))
            
    except Exception as e:
        logging.error(f"Error scraping {url}: {e}")
        return [], []

def main():
    print("🚀 Starting Simple Website Scraper")
    print("=" * 50)
    
    # Load Excel file
    df = pd.read_excel(INPUT_FILE)
    df.columns = df.columns.str.strip()
    print(f"📁 Loaded {len(df)} URLs from Excel file")
    
    if 'Website URL' not in df.columns:
        print("❌ Error: Excel file must have a column named 'Website URL'")
        return
    
    results = []
    successful = 0
    
    for idx, row in df.iterrows():
        website_url = str(row['Website URL']).strip()
        normalized_url = normalize_url(website_url)
        
        print(f"\n🔍 Processing {idx + 1}/{len(df)}: {website_url}")
        
        if normalized_url:
            try:
                emails, phones = scrape_website(normalized_url)
                
                if emails or phones:
                    successful += 1
                    print(f"✅ Found: {len(emails)} emails, {len(phones)} phones")
                else:
                    print("❌ No contact info found")
                
                results.append({
                    "Website URL": website_url,
                    "Website Emails": ", ".join(emails) if emails else "N/A",
                    "Website Phones": ", ".join(phones) if phones else "N/A"
                })
                
            except Exception as e:
                print(f"❌ Error: {str(e)[:50]}...")
                results.append({
                    "Website URL": website_url,
                    "Website Emails": "N/A",
                    "Website Phones": "N/A"
                })
        else:
            print("❌ Invalid URL")
            results.append({
                "Website URL": website_url,
                "Website Emails": "N/A",
                "Website Phones": "N/A"
            })
        
        # Small delay to be respectful
        time.sleep(random.uniform(1, 3))
    
    # Save results
    result_df = pd.DataFrame(results)
    result_df.to_excel(OUTPUT_FILE, index=False)
    
    print(f"\n🎉 COMPLETED!")
    print(f"📊 Total URLs: {len(df)}")
    print(f"✅ Successful: {successful}")
    print(f"📁 Results saved to: {OUTPUT_FILE}")

if __name__ == "__main__":
    main()
