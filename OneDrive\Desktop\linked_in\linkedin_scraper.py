import pandas as pd
import re
import phonenumbers
from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright
import logging
import time
import random

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# File paths
INPUT_FILE = r"C:\Users\<USER>\Downloads\linkedin_urls.xlsx"  # Your LinkedIn URLs file
OUTPUT_FILE = r"C:\Users\<USER>\Downloads\linkedin_scraped_data.xlsx"

def extract_emails(text, soup=None):
    """Extract emails from text and HTML."""
    emails = set()
    
    if not text:
        return []
    
    # Email patterns
    email_patterns = [
        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
        r'[A-Za-z0-9._%+-]+\s*@\s*[A-Za-z0-9.-]+\s*\.\s*[A-Za-z]{2,}',
        r'[A-Za-z0-9._%+-]+\[at\][A-Za-z0-9.-]+\[dot\][A-Za-z]{2,}',
        r'[A-Za-z0-9._%+-]+\(at\)[A-Za-z0-9.-]+\(dot\)[A-Za-z]{2,}',
    ]
    
    for pattern in email_patterns:
        try:
            found_emails = re.findall(pattern, text, re.IGNORECASE)
            for email in found_emails:
                clean_email = email.replace('[at]', '@').replace('[dot]', '.').replace('(at)', '@').replace('(dot)', '.')
                clean_email = re.sub(r'\s+', '', clean_email).lower()
                
                if '@' in clean_email and '.' in clean_email.split('@')[1]:
                    if not re.search(r'\.(png|jpg|jpeg|gif|svg|pdf|css|js|ico)$', clean_email):
                        emails.add(clean_email)
        except:
            continue
    
    # Extract from mailto links
    if soup:
        try:
            mailto_links = soup.find_all('a', href=re.compile(r'^mailto:', re.IGNORECASE))
            for link in mailto_links:
                href = link.get('href', '')
                email = href.replace('mailto:', '').split('?')[0].lower()
                if '@' in email and '.' in email.split('@')[1]:
                    emails.add(email)
        except:
            pass
    
    return sorted(list(emails))

def extract_phones(text, soup=None):
    """Extract phone numbers from text and HTML."""
    phones = set()
    
    if not text:
        return []
    
    # Phone patterns
    phone_patterns = [
        r'\+?1?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
        r'\+?([1-9]\d{0,3})[-.\s]?\(?([0-9]{1,4})\)?[-.\s]?([0-9]{1,4})[-.\s]?([0-9]{1,9})',
        r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
        r'\b\d{10}\b',
        r'\+\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,9}',
        r'\(\d{3}\)\s?\d{3}[-.]?\d{4}',
    ]
    
    for pattern in phone_patterns:
        try:
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    phone = ''.join(match)
                else:
                    phone = match
                
                cleaned = re.sub(r'[^\d+]', '', phone)
                if 7 <= len(cleaned) <= 15:
                    if not re.match(r'^(0+|1+|2+|3+|4+|5+|6+|7+|8+|9+)$', cleaned):
                        phones.add(cleaned)
        except:
            continue
    
    # Extract from tel links
    if soup:
        try:
            tel_links = soup.find_all('a', href=re.compile(r'^tel:', re.IGNORECASE))
            for link in tel_links:
                href = link.get('href', '')
                phone = re.sub(r'[^\d+]', '', href.replace('tel:', ''))
                if 7 <= len(phone) <= 15:
                    phones.add(phone)
        except:
            pass
    
    # Format phones
    formatted_phones = set()
    for phone in phones:
        try:
            for region in ['US', 'GB', 'IN', 'CA', 'AU', 'DE', 'FR', 'IT', 'ES', 'BR']:
                try:
                    parsed = phonenumbers.parse(phone, region)
                    if phonenumbers.is_valid_number(parsed):
                        formatted = phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
                        formatted_phones.add(formatted)
                        break
                except:
                    continue
            else:
                if len(phone) >= 10:
                    formatted_phones.add(phone)
        except:
            if len(phone) >= 10:
                formatted_phones.add(phone)
    
    return sorted(list(formatted_phones))

def extract_website_from_linkedin(soup):
    """Extract company website URL from LinkedIn page."""
    website_patterns = [
        r'https?://(?:www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(?:/[^\s]*)?',
        r'(?:www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}',
    ]
    
    # Look for website in specific LinkedIn elements
    website_selectors = [
        'a[href*="http"]:not([href*="linkedin.com"])',
        '[data-test-id="about-us-website"]',
        '.org-about-us-organization-description__website',
        '.org-top-card-summary-info-list__info-item'
    ]
    
    websites = set()
    
    # Method 1: Look in specific elements
    for selector in website_selectors:
        try:
            elements = soup.select(selector)
            for elem in elements:
                href = elem.get('href', '')
                text = elem.get_text(strip=True)
                
                for content in [href, text]:
                    if content:
                        for pattern in website_patterns:
                            matches = re.findall(pattern, content, re.IGNORECASE)
                            for match in matches:
                                if isinstance(match, tuple):
                                    website = ''.join(match)
                                else:
                                    website = match
                                
                                website = website.strip().lower()
                                if website and not any(skip in website for skip in [
                                    'linkedin.com', 'facebook.com', 'twitter.com', 'instagram.com',
                                    'youtube.com', 'google.com', 'microsoft.com', 'apple.com'
                                ]):
                                    if '.' in website and len(website) > 4:
                                        if not website.startswith('http'):
                                            website = 'https://' + website
                                        websites.add(website)
        except:
            continue
    
    # Method 2: Look in page text
    try:
        page_text = soup.get_text()
        for pattern in website_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    website = ''.join(match)
                else:
                    website = match
                
                website = website.strip().lower()
                if website and not any(skip in website for skip in [
                    'linkedin.com', 'facebook.com', 'twitter.com', 'instagram.com'
                ]):
                    if '.' in website and len(website) > 4:
                        if not website.startswith('http'):
                            website = 'https://' + website
                        websites.add(website)
    except:
        pass
    
    return list(websites)

def scrape_website(url):
    """Scrape company website for emails and phones."""
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            page = context.new_page()
            page.set_default_timeout(30000)
            
            # Navigate to website
            try:
                page.goto(url, wait_until='domcontentloaded', timeout=30000)
                page.wait_for_load_state('networkidle', timeout=10000)
            except:
                browser.close()
                return [], []
            
            html = page.content()
            soup = BeautifulSoup(html, 'lxml')
            text = soup.get_text(separator=' ', strip=True)
            
            # Extract from main page
            emails = extract_emails(text, soup)
            phones = extract_phones(text, soup)
            
            # Try contact page
            try:
                contact_links = soup.find_all('a', href=re.compile(r'contact|about', re.IGNORECASE))
                for link in contact_links[:2]:
                    href = link.get('href', '')
                    if href and not href.startswith('#'):
                        try:
                            if href.startswith('/'):
                                contact_url = url + href
                            elif href.startswith('http'):
                                contact_url = href
                            else:
                                contact_url = url + '/' + href.lstrip('/')
                            
                            page.goto(contact_url, wait_until='domcontentloaded', timeout=15000)
                            contact_html = page.content()
                            contact_soup = BeautifulSoup(contact_html, 'lxml')
                            contact_text = contact_soup.get_text(separator=' ', strip=True)
                            
                            contact_emails = extract_emails(contact_text, contact_soup)
                            contact_phones = extract_phones(contact_text, contact_soup)
                            emails.extend(contact_emails)
                            phones.extend(contact_phones)
                        except:
                            continue
            except:
                pass
            
            browser.close()
            return list(set(emails)), list(set(phones))
            
    except Exception as e:
        logging.error(f"Error scraping website {url}: {e}")
        return [], []

def scrape_linkedin_profile(linkedin_url):
    """Scrape LinkedIn company profile for emails, phones, and website."""
    all_emails = set()
    all_phones = set()
    company_website = None
    
    print(f"🔍 Scraping LinkedIn: {linkedin_url}")
    
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            page = context.new_page()
            page.set_default_timeout(30000)
            
            # Navigate to LinkedIn page
            try:
                page.goto(linkedin_url, wait_until='domcontentloaded', timeout=30000)
                page.wait_for_load_state('networkidle', timeout=10000)
            except Exception as e:
                print(f"❌ Failed to load LinkedIn page: {e}")
                browser.close()
                return [], [], None
            
            html = page.content()
            soup = BeautifulSoup(html, 'lxml')
            text = soup.get_text(separator=' ', strip=True)
            
            # Extract emails and phones from LinkedIn page
            linkedin_emails = extract_emails(text, soup)
            linkedin_phones = extract_phones(text, soup)
            all_emails.update(linkedin_emails)
            all_phones.update(linkedin_phones)
            
            print(f"📄 LinkedIn page: {len(linkedin_emails)} emails, {len(linkedin_phones)} phones")
            
            # Extract company website
            websites = extract_website_from_linkedin(soup)
            if websites:
                company_website = websites[0]  # Take the first website found
                print(f"🌐 Found company website: {company_website}")
                
                # Scrape the company website
                website_emails, website_phones = scrape_website(company_website)
                all_emails.update(website_emails)
                all_phones.update(website_phones)
                
                print(f"🏢 Website: {len(website_emails)} emails, {len(website_phones)} phones")
            else:
                print("❌ No company website found")
            
            browser.close()
            
            final_emails = sorted(list(all_emails))
            final_phones = sorted(list(all_phones))
            
            print(f"✅ TOTAL: {len(final_emails)} emails, {len(final_phones)} phones")
            
            return final_emails, final_phones, company_website
            
    except Exception as e:
        print(f"❌ Error scraping LinkedIn: {e}")
        return [], [], None

def main():
    """Main function to scrape ONE LinkedIn profile."""
    print("🚀 SINGLE LINKEDIN COMPANY SCRAPER")
    print("=" * 50)

    # Get LinkedIn URL from user input
    linkedin_url = input("� Enter LinkedIn company URL: ").strip()

    if not linkedin_url:
        linkedin_url = "https://linkedin.com/company/cooperativa-ciencia"  # Default example
        print(f"📝 Using example URL: {linkedin_url}")

    print(f"\n{'='*60}")
    print(f"🎯 SCRAPING: {linkedin_url}")
    print(f"{'='*60}")

    try:
        # Scrape the LinkedIn profile
        emails, phones, website = scrape_linkedin_profile(linkedin_url)

        # Create result
        result = {
            "LinkedIn URL": linkedin_url,
            "Company Website": website if website else "N/A",
            "Website Emails": ", ".join(emails) if emails else "N/A",
            "Website Phones": ", ".join(phones) if phones else "N/A"
        }

        # Save to Excel
        result_df = pd.DataFrame([result])
        result_df.to_excel(OUTPUT_FILE, index=False)

        # Display results
        print(f"\n🎉 SCRAPING COMPLETED!")
        print(f"=" * 50)
        print(f"🔗 LinkedIn URL: {linkedin_url}")
        if website and website != "N/A":
            print(f"🌐 Company Website: {website}")
        else:
            print(f"🌐 Company Website: Not found")

        if emails:
            print(f"📧 Emails Found: {len(emails)}")
            for email in emails:
                print(f"   • {email}")
        else:
            print(f"📧 Emails Found: None")

        if phones:
            print(f"📞 Phones Found: {len(phones)}")
            for phone in phones:
                print(f"   • {phone}")
        else:
            print(f"📞 Phones Found: None")

        print(f"\n💾 Results saved to: {OUTPUT_FILE}")
        print(f"=" * 50)

        if emails or phones:
            print(f"✅ SUCCESS: Found contact information!")
        else:
            print(f"❌ No contact information found")
            print(f"� Try a different LinkedIn company URL")

    except Exception as e:
        print(f"❌ Script failed: {e}")

if __name__ == "__main__":
    main()
