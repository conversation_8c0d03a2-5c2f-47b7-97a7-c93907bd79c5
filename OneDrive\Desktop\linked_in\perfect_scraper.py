import pandas as pd
import re
import phonenumbers
from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright
import logging
import random
import time

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# File paths
INPUT_FILE = r"C:\Users\<USER>\Downloads\real_company_websites.xlsx"  # Using real company websites
OUTPUT_FILE = r"C:\Users\<USER>\Downloads\website_scraped_data.xlsx"

def extract_emails_advanced(text, soup):
    """Advanced email extraction from text and HTML."""
    emails = set()
    
    # Pattern 1: Standard email regex
    email_patterns = [
        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
        r'[A-Za-z0-9._%+-]+\s*@\s*[A-Za-z0-9.-]+\s*\.\s*[A-Za-z]{2,}',
        r'[A-Za-z0-9._%+-]+\[at\][A-Za-z0-9.-]+\[dot\][A-Za-z]{2,}',
        r'[A-Za-z0-9._%+-]+\(at\)[A-Za-z0-9.-]+\(dot\)[A-Za-z]{2,}',
    ]
    
    for pattern in email_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            clean_email = match.replace('[at]', '@').replace('[dot]', '.').replace('(at)', '@').replace('(dot)', '.')
            clean_email = re.sub(r'\s+', '', clean_email).lower()
            if '@' in clean_email and '.' in clean_email.split('@')[1]:
                if not re.search(r'\.(png|jpg|jpeg|gif|svg|pdf|css|js|ico)$', clean_email):
                    emails.add(clean_email)
    
    # Pattern 2: Extract from mailto links
    try:
        mailto_links = soup.find_all('a', href=re.compile(r'^mailto:', re.IGNORECASE))
        for link in mailto_links:
            href = link.get('href', '')
            email = href.replace('mailto:', '').split('?')[0].split('&')[0].lower()
            if '@' in email and '.' in email.split('@')[1]:
                emails.add(email)
    except:
        pass
    
    # Pattern 3: Look for emails in specific HTML attributes
    try:
        email_elements = soup.find_all(attrs={'data-email': True})
        for elem in email_elements:
            email = elem.get('data-email', '').lower()
            if '@' in email and '.' in email.split('@')[1]:
                emails.add(email)
    except:
        pass
    
    # Pattern 4: Look in contact forms and input fields
    try:
        contact_inputs = soup.find_all('input', {'type': 'email'})
        for inp in contact_inputs:
            placeholder = inp.get('placeholder', '').lower()
            value = inp.get('value', '').lower()
            for text_val in [placeholder, value]:
                if '@' in text_val and '.' in text_val.split('@')[1]:
                    emails.add(text_val)
    except:
        pass
    
    return sorted(list(emails))

def extract_phones_advanced(text, soup):
    """Advanced phone extraction from text and HTML."""
    phones = set()
    
    # Pattern 1: Various phone number formats
    phone_patterns = [
        r'\+?1?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',  # US format
        r'\+?([1-9]\d{0,3})[-.\s]?\(?([0-9]{1,4})\)?[-.\s]?([0-9]{1,4})[-.\s]?([0-9]{1,9})',  # International
        r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # Simple US
        r'\b\d{10}\b',  # 10 digits
        r'\+\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,9}',  # International with +
        r'\(\d{3}\)\s?\d{3}[-.]?\d{4}',  # (xxx) xxx-xxxx
    ]
    
    for pattern in phone_patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            if isinstance(match, tuple):
                phone = ''.join(match)
            else:
                phone = match
            
            # Clean phone number
            cleaned = re.sub(r'[^\d+]', '', phone)
            if 7 <= len(cleaned) <= 15:
                if not re.match(r'^(0+|1+|2+|3+|4+|5+|6+|7+|8+|9+)$', cleaned):
                    phones.add(cleaned)
    
    # Pattern 2: Extract from tel links
    try:
        tel_links = soup.find_all('a', href=re.compile(r'^tel:', re.IGNORECASE))
        for link in tel_links:
            href = link.get('href', '')
            phone = re.sub(r'[^\d+]', '', href.replace('tel:', ''))
            if 7 <= len(phone) <= 15:
                phones.add(phone)
    except:
        pass
    
    # Pattern 3: Look for phones in specific HTML attributes
    try:
        phone_elements = soup.find_all(attrs={'data-phone': True})
        for elem in phone_elements:
            phone = re.sub(r'[^\d+]', '', elem.get('data-phone', ''))
            if 7 <= len(phone) <= 15:
                phones.add(phone)
    except:
        pass
    
    # Format phones using phonenumbers library
    formatted_phones = set()
    for phone in phones:
        try:
            for region in ['US', 'GB', 'IN', 'CA', 'AU', 'DE', 'FR', 'IT', 'ES', 'BR', 'MX']:
                try:
                    parsed = phonenumbers.parse(phone, region)
                    if phonenumbers.is_valid_number(parsed):
                        formatted = phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
                        formatted_phones.add(formatted)
                        break
                except:
                    continue
            else:
                if len(phone) >= 10:
                    formatted_phones.add(phone)
        except:
            if len(phone) >= 10:
                formatted_phones.add(phone)
    
    return sorted(list(formatted_phones))

def scrape_website_comprehensive(url):
    """Comprehensive website scraping for emails and phones."""
    all_emails = set()
    all_phones = set()
    
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080}
            )
            page = context.new_page()
            page.set_default_timeout(45000)
            
            # Navigate to main page
            try:
                response = page.goto(url, wait_until='domcontentloaded', timeout=45000)
                if response and response.status >= 400:
                    logging.warning(f"HTTP {response.status} for {url}")
                    return [], []
            except Exception as e:
                logging.error(f"Failed to load {url}: {e}")
                return [], []
            
            # Wait for page to load
            try:
                page.wait_for_load_state('networkidle', timeout=15000)
            except:
                pass
            
            # Get page content
            html = page.content()
            soup = BeautifulSoup(html, 'lxml')
            
            # Extract all text content
            text_content = soup.get_text(separator=' ', strip=True)
            
            # Extract from main page
            main_emails = extract_emails_advanced(text_content, soup)
            main_phones = extract_phones_advanced(text_content, soup)
            all_emails.update(main_emails)
            all_phones.update(main_phones)
            
            # Find and scrape contact-related pages
            contact_urls = set()
            contact_patterns = [
                r'contact', r'about', r'info', r'reach', r'connect', 
                r'support', r'help', r'team', r'staff', r'office'
            ]
            
            for pattern in contact_patterns:
                try:
                    contact_links = soup.find_all('a', href=re.compile(pattern, re.IGNORECASE))
                    for link in contact_links[:2]:  # Limit to 2 per pattern
                        href = link.get('href', '')
                        if href and not href.startswith('#') and not href.startswith('mailto:') and not href.startswith('tel:'):
                            if href.startswith('http'):
                                contact_urls.add(href)
                            elif href.startswith('/'):
                                contact_urls.add(url.rstrip('/') + href)
                            else:
                                contact_urls.add(url.rstrip('/') + '/' + href.lstrip('/'))
                except:
                    continue
            
            # Scrape contact pages
            for contact_url in list(contact_urls)[:5]:  # Limit to 5 contact pages
                try:
                    logging.info(f"Scraping contact page: {contact_url}")
                    page.goto(contact_url, wait_until='domcontentloaded', timeout=30000)
                    page.wait_for_load_state('networkidle', timeout=10000)
                    
                    contact_html = page.content()
                    contact_soup = BeautifulSoup(contact_html, 'lxml')
                    contact_text = contact_soup.get_text(separator=' ', strip=True)
                    
                    # Extract from contact page
                    contact_emails = extract_emails_advanced(contact_text, contact_soup)
                    contact_phones = extract_phones_advanced(contact_text, contact_soup)
                    all_emails.update(contact_emails)
                    all_phones.update(contact_phones)
                    
                except Exception as e:
                    logging.debug(f"Failed to scrape contact page {contact_url}: {e}")
                    continue
            
            browser.close()
            
            final_emails = sorted(list(all_emails))
            final_phones = sorted(list(all_phones))
            
            return final_emails, final_phones
            
    except Exception as e:
        logging.error(f"Error scraping {url}: {e}")
        return [], []

def normalize_url(url):
    """Normalize URL."""
    if not url or str(url).strip().lower() in ['n/a', 'na', '', 'none', 'null', 'nan']:
        return None
    
    url = str(url).strip()
    
    # Skip Bing Maps URLs - they won't have contact info
    if 'bing.com/maps' in url.lower():
        return None
    
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    return url.rstrip('/')

def main():
    """Main scraping function."""
    print("🚀 PERFECT EMAIL & PHONE SCRAPER")
    print("=" * 60)
    
    try:
        # Load Excel file
        df = pd.read_excel(INPUT_FILE)
        df.columns = df.columns.str.strip()
        print(f"📁 Loaded {len(df)} URLs from Excel file")
        
        if 'Website URL' not in df.columns:
            print("❌ Error: Excel file must have a column named 'Website URL'")
            return
        
        results = []
        successful = 0
        skipped_maps = 0
        
        for idx, row in df.iterrows():
            website_url = str(row['Website URL']).strip()
            
            # Skip Bing Maps URLs
            if 'bing.com/maps' in website_url.lower():
                skipped_maps += 1
                print(f"\n⏭️  Skipping {idx + 1}/{len(df)}: Bing Maps URL (no contact info)")
                results.append({
                    "Website URL": website_url,
                    "Website Emails": "N/A (Map URL)",
                    "Website Phones": "N/A (Map URL)"
                })
                continue
            
            normalized_url = normalize_url(website_url)
            
            print(f"\n🔍 Processing {idx + 1}/{len(df)}: {website_url}")
            
            if normalized_url:
                try:
                    emails, phones = scrape_website_comprehensive(normalized_url)
                    
                    if emails or phones:
                        successful += 1
                        print(f"✅ SUCCESS: Found {len(emails)} emails, {len(phones)} phones")
                        if emails:
                            print(f"   📧 Emails: {', '.join(emails[:3])}{'...' if len(emails) > 3 else ''}")
                        if phones:
                            print(f"   📞 Phones: {', '.join(phones[:3])}{'...' if len(phones) > 3 else ''}")
                    else:
                        print("❌ No contact info found")
                    
                    results.append({
                        "Website URL": website_url,
                        "Website Emails": ", ".join(emails) if emails else "N/A",
                        "Website Phones": ", ".join(phones) if phones else "N/A"
                    })
                    
                except Exception as e:
                    print(f"❌ Error: {str(e)[:50]}...")
                    results.append({
                        "Website URL": website_url,
                        "Website Emails": "N/A",
                        "Website Phones": "N/A"
                    })
            else:
                print("❌ Invalid URL")
                results.append({
                    "Website URL": website_url,
                    "Website Emails": "N/A",
                    "Website Phones": "N/A"
                })
            
            # Small delay
            time.sleep(random.uniform(2, 4))
        
        # Save results
        result_df = pd.DataFrame(results)
        result_df.to_excel(OUTPUT_FILE, index=False)
        
        # Print final summary
        print(f"\n🎉 SCRAPING COMPLETED!")
        print(f"📊 Total URLs processed: {len(df)}")
        print(f"⏭️  Skipped Bing Maps URLs: {skipped_maps}")
        print(f"✅ Successful extractions: {successful}")
        print(f"📈 Success rate: {(successful/(len(df)-skipped_maps))*100:.1f}% (excluding maps)")
        print(f"💾 Results saved to: {OUTPUT_FILE}")
        
        if skipped_maps > 0:
            print(f"\n⚠️  NOTE: {skipped_maps} Bing Maps URLs were skipped because they don't contain company contact info.")
            print("   For better results, use actual company website URLs instead of map locations.")
        
    except Exception as e:
        print(f"❌ Script failed: {e}")
        logging.error(f"Script failed: {e}")

if __name__ == "__main__":
    main()
