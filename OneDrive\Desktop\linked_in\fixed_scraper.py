import pandas as pd
import re
import phonenumbers
from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright
import logging
import random
import time
import sys

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# File paths
INPUT_FILE = r"C:\Users\<USER>\Downloads\website_urls_output.xlsx"
OUTPUT_FILE = r"C:\Users\<USER>\Downloads\website_scraped_data.xlsx"

def extract_emails(text):
    """Extract emails from text."""
    if not text:
        return []
    
    email_patterns = [
        r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}",
        r"[a-zA-Z0-9._%+-]+\[at\][a-zA-Z0-9.-]+\[dot\][a-zA-Z]{2,}",
        r"[a-zA-Z0-9._%+-]+\(at\)[a-zA-Z0-9.-]+\(dot\)[a-zA-Z]{2,}",
    ]
    
    emails = set()
    for pattern in email_patterns:
        try:
            found_emails = re.findall(pattern, text, re.IGNORECASE)
            for email in found_emails:
                # Clean obfuscated emails
                clean_email = email.replace('[at]', '@').replace('[dot]', '.').replace('(at)', '@').replace('(dot)', '.')
                clean_email = re.sub(r'\s+', '', clean_email).lower()
                
                # Basic validation
                if '@' in clean_email and '.' in clean_email.split('@')[1]:
                    if not re.search(r'\.(png|jpg|jpeg|gif|svg|pdf|css|js)$', clean_email):
                        emails.add(clean_email)
        except Exception as e:
            logging.debug(f"Error in email extraction: {e}")
            continue
    
    return sorted(list(emails))

def extract_phones(text):
    """Extract phone numbers from text."""
    if not text:
        return []
    
    phone_patterns = [
        r"(\+?\d{1,4}[\s.-]?\(?\d{3,4}\)?[\s.-]?\d{3,4}[\s.-]?\d{3,4})",
        r"(\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4})",
        r"(\+?\d{10,15})",
        r"(\d{10})",
    ]
    
    phones = set()
    for pattern in phone_patterns:
        try:
            found_phones = re.findall(pattern, text)
            for phone in found_phones:
                # Clean phone number
                cleaned = re.sub(r'[^\d+]', '', phone)
                
                # Basic validation
                if 7 <= len(cleaned) <= 15:
                    if not re.match(r'^(0+|1+|2+|3+|4+|5+|6+|7+|8+|9+)$', cleaned):
                        # Try to format with phonenumbers
                        try:
                            for region in ['US', 'GB', 'IN', 'CA', 'AU']:
                                try:
                                    parsed = phonenumbers.parse(cleaned, region)
                                    if phonenumbers.is_valid_number(parsed):
                                        formatted = phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
                                        phones.add(formatted)
                                        break
                                except:
                                    continue
                            else:
                                if len(cleaned) >= 10:
                                    phones.add(cleaned)
                        except:
                            if len(cleaned) >= 10:
                                phones.add(cleaned)
        except Exception as e:
            logging.debug(f"Error in phone extraction: {e}")
            continue
    
    return sorted(list(phones))

def normalize_url(url):
    """Normalize URL."""
    try:
        if not url or str(url).strip().lower() in ['n/a', 'na', '', 'none', 'null', 'nan']:
            return None
        
        url = str(url).strip()
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        return url.rstrip('/')
    except:
        return None

def scrape_website(url):
    """Scrape emails and phones from website."""
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            page = context.new_page()
            page.set_default_timeout(30000)
            
            # Navigate to page
            try:
                page.goto(url, wait_until='domcontentloaded', timeout=30000)
                page.wait_for_load_state('networkidle', timeout=10000)
            except Exception as e:
                logging.warning(f"Page load issue for {url}: {e}")
            
            # Get page content
            html = page.content()
            soup = BeautifulSoup(html, 'lxml')
            
            # Extract text
            text_elements = soup.find_all([
                'p', 'div', 'span', 'a', 'li', 'section', 'footer', 'header', 
                'address', 'td', 'th', 'h1', 'h2', 'h3'
            ])
            
            text = ' '.join(tag.get_text(separator=' ', strip=True) for tag in text_elements if tag.get_text(strip=True))
            
            # Extract from links
            all_emails = set()
            all_phones = set()
            
            # Mailto links
            try:
                mailto_links = soup.find_all('a', href=re.compile(r'^mailto:', re.IGNORECASE))
                for link in mailto_links:
                    href = link.get('href', '')
                    email = href.replace('mailto:', '').split('?')[0].lower()
                    if '@' in email and '.' in email.split('@')[1]:
                        all_emails.add(email)
            except:
                pass
            
            # Tel links
            try:
                tel_links = soup.find_all('a', href=re.compile(r'^tel:', re.IGNORECASE))
                for link in tel_links:
                    href = link.get('href', '')
                    phone = re.sub(r'[^\d+]', '', href.replace('tel:', ''))
                    if len(phone) >= 7:
                        all_phones.add(phone)
            except:
                pass
            
            # Extract from text
            page_emails = extract_emails(text)
            page_phones = extract_phones(text)
            all_emails.update(page_emails)
            all_phones.update(page_phones)
            
            # Try contact pages
            try:
                contact_links = soup.find_all('a', href=re.compile(r'contact|about', re.IGNORECASE))
                for link in contact_links[:2]:
                    href = link.get('href', '')
                    if href and not href.startswith('#'):
                        try:
                            if href.startswith('/'):
                                contact_url = url + href
                            elif href.startswith('http'):
                                contact_url = href
                            else:
                                contact_url = url + '/' + href.lstrip('/')
                            
                            page.goto(contact_url, wait_until='domcontentloaded', timeout=15000)
                            contact_html = page.content()
                            contact_soup = BeautifulSoup(contact_html, 'lxml')
                            
                            contact_elements = contact_soup.find_all(['p', 'div', 'span', 'a', 'address'])
                            contact_text = ' '.join(tag.get_text(separator=' ', strip=True) 
                                                  for tag in contact_elements if tag.get_text(strip=True))
                            
                            contact_emails = extract_emails(contact_text)
                            contact_phones = extract_phones(contact_text)
                            all_emails.update(contact_emails)
                            all_phones.update(contact_phones)
                            
                        except:
                            continue
            except:
                pass
            
            browser.close()
            return sorted(list(all_emails)), sorted(list(all_phones))
            
    except Exception as e:
        logging.error(f"Error scraping {url}: {e}")
        return [], []

def main():
    """Main function."""
    print("🚀 Website Scraper Starting...")
    print("=" * 50)
    
    try:
        # Check if input file exists
        try:
            df = pd.read_excel(INPUT_FILE)
        except FileNotFoundError:
            print(f"❌ Error: Input file not found: {INPUT_FILE}")
            print("Please make sure the file exists and try again.")
            return
        except Exception as e:
            print(f"❌ Error reading Excel file: {e}")
            return
        
        df.columns = df.columns.str.strip()
        print(f"📁 Loaded {len(df)} URLs")
        print(f"📋 Columns found: {list(df.columns)}")
        
        if 'Website URL' not in df.columns:
            print("❌ Error: Excel file must have a column named 'Website URL'")
            print(f"Available columns: {list(df.columns)}")
            return
        
        results = []
        successful = 0
        total = len(df)
        
        for idx, row in df.iterrows():
            try:
                website_url = str(row['Website URL']).strip()
                normalized_url = normalize_url(website_url)
                
                print(f"\n🔍 Processing {idx + 1}/{total}: {website_url}")
                
                if normalized_url:
                    emails, phones = scrape_website(normalized_url)
                    
                    if emails or phones:
                        successful += 1
                        print(f"✅ Found: {len(emails)} emails, {len(phones)} phones")
                    else:
                        print("❌ No contact info found")
                    
                    results.append({
                        "Website URL": website_url,
                        "Website Emails": ", ".join(emails) if emails else "N/A",
                        "Website Phones": ", ".join(phones) if phones else "N/A"
                    })
                    
                else:
                    print("❌ Invalid URL")
                    results.append({
                        "Website URL": website_url,
                        "Website Emails": "N/A",
                        "Website Phones": "N/A"
                    })
                
                # Small delay
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                print(f"❌ Error processing row {idx + 1}: {e}")
                results.append({
                    "Website URL": str(row.get('Website URL', 'N/A')),
                    "Website Emails": "N/A",
                    "Website Phones": "N/A"
                })
        
        # Save results
        try:
            result_df = pd.DataFrame(results)
            result_df.to_excel(OUTPUT_FILE, index=False)
            
            print(f"\n🎉 COMPLETED!")
            print(f"📊 Total URLs: {total}")
            print(f"✅ Successful: {successful}")
            print(f"📈 Success rate: {(successful/total)*100:.1f}%")
            print(f"💾 Results saved to: {OUTPUT_FILE}")
            
        except Exception as e:
            print(f"❌ Error saving results: {e}")
            
    except Exception as e:
        print(f"❌ Script failed: {e}")
        logging.error(f"Script failed: {e}")

if __name__ == "__main__":
    main()
