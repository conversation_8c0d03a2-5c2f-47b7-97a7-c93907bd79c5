import pandas as pd
import re
from playwright.sync_api import sync_playwright
import logging
import time
import random

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

INPUT_FILE = r"C:\Users\<USER>\Downloads\website_urls_output.xlsx"
OUTPUT_FILE = r"C:\Users\<USER>\Downloads\real_company_websites.xlsx"

def extract_website_from_linkedin(linkedin_url):
    """Extract actual company website from LinkedIn company page."""
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            page = context.new_page()
            page.set_default_timeout(30000)
            
            # Navigate to LinkedIn page
            try:
                page.goto(linkedin_url, wait_until='domcontentloaded', timeout=30000)
                page.wait_for_load_state('networkidle', timeout=10000)
            except Exception as e:
                logging.warning(f"Failed to load LinkedIn page {linkedin_url}: {e}")
                browser.close()
                return None
            
            # Look for website links on LinkedIn page
            html = page.content()
            
            # Method 1: Look for website links in the page
            website_patterns = [
                r'https?://(?:www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(?:/[^\s]*)?',
                r'(?:www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}',
            ]
            
            websites = set()
            for pattern in website_patterns:
                matches = re.findall(pattern, html, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        website = ''.join(match)
                    else:
                        website = match
                    
                    # Clean and validate website
                    website = website.strip().lower()
                    if website and not any(skip in website for skip in [
                        'linkedin.com', 'facebook.com', 'twitter.com', 'instagram.com',
                        'youtube.com', 'google.com', 'microsoft.com', 'apple.com',
                        'bing.com', 'maps.', 'mailto:', 'tel:', '.png', '.jpg', '.pdf'
                    ]):
                        if '.' in website and len(website) > 4:
                            if not website.startswith('http'):
                                website = 'https://' + website
                            websites.add(website)
            
            browser.close()
            
            # Return the most likely company website
            if websites:
                # Prefer .com domains
                com_sites = [w for w in websites if '.com' in w]
                if com_sites:
                    return sorted(com_sites)[0]
                else:
                    return sorted(websites)[0]
            
            return None
            
    except Exception as e:
        logging.error(f"Error extracting website from {linkedin_url}: {e}")
        return None

def create_sample_company_websites():
    """Create a sample file with real company websites for testing."""
    sample_companies = [
        'https://www.microsoft.com',
        'https://www.apple.com',
        'https://www.google.com',
        'https://www.amazon.com',
        'https://www.tesla.com',
        'https://www.netflix.com',
        'https://www.adobe.com',
        'https://www.salesforce.com',
        'https://www.oracle.com',
        'https://www.ibm.com',
        'https://www.intel.com',
        'https://www.nvidia.com',
        'https://www.cisco.com',
        'https://www.hp.com',
        'https://www.dell.com',
        'https://www.zoom.us',
        'https://www.slack.com',
        'https://www.dropbox.com',
        'https://www.spotify.com',
        'https://www.uber.com',
        'https://www.airbnb.com',
        'https://www.paypal.com',
        'https://www.shopify.com',
        'https://www.square.com',
        'https://www.stripe.com',
        'https://www.twilio.com',
        'https://www.github.com',
        'https://www.atlassian.com',
        'https://www.zendesk.com',
        'https://www.hubspot.com'
    ]
    
    df = pd.DataFrame({'Website URL': sample_companies})
    sample_file = r"C:\Users\<USER>\Downloads\sample_company_websites.xlsx"
    df.to_excel(sample_file, index=False)
    return sample_file

def main():
    """Main function to extract real websites or create sample data."""
    print("🚀 LinkedIn to Real Websites Converter")
    print("=" * 50)
    
    try:
        # Load your current file
        df = pd.read_excel(INPUT_FILE)
        df.columns = df.columns.str.strip()
        print(f"📁 Loaded {len(df)} URLs from your file")
        print(f"📋 Columns: {list(df.columns)}")
        
        # Check what type of URLs you have
        if 'LinkedIn URL' in df.columns:
            linkedin_urls = df['LinkedIn URL'].dropna()
            print(f"🔗 Found {len(linkedin_urls)} LinkedIn URLs")
            
            # Option 1: Extract websites from LinkedIn (slower but real data)
            print("\n🤔 Choose an option:")
            print("1. Extract real websites from LinkedIn URLs (slow but accurate)")
            print("2. Create sample file with real company websites (fast for testing)")
            
            choice = input("Enter your choice (1 or 2): ").strip()
            
            if choice == "1":
                print("\n🔍 Extracting real websites from LinkedIn URLs...")
                results = []
                
                for idx, linkedin_url in enumerate(linkedin_urls.head(10)):  # Limit to first 10 for demo
                    print(f"Processing {idx + 1}/10: {linkedin_url}")
                    website = extract_website_from_linkedin(str(linkedin_url))
                    
                    results.append({
                        'LinkedIn URL': linkedin_url,
                        'Website URL': website if website else 'N/A'
                    })
                    
                    time.sleep(random.uniform(2, 5))  # Be respectful to LinkedIn
                
                # Save results
                result_df = pd.DataFrame(results)
                result_df.to_excel(OUTPUT_FILE, index=False)
                print(f"✅ Saved real websites to: {OUTPUT_FILE}")
                
            else:
                # Option 2: Create sample file
                sample_file = create_sample_company_websites()
                print(f"✅ Created sample file with 30 real company websites: {sample_file}")
                print("\n🎯 You can now use this sample file to test the scraper!")
                print("   It contains real company websites that will have contact info.")
        
        else:
            # No LinkedIn URLs found, create sample file
            print("❌ No LinkedIn URLs found in your file.")
            sample_file = create_sample_company_websites()
            print(f"✅ Created sample file with real company websites: {sample_file}")
        
        print(f"\n📋 Next Steps:")
        print(f"1. Use the sample file to test the scraper: python perfect_scraper.py")
        print(f"2. Update the INPUT_FILE path in perfect_scraper.py to use the sample file")
        print(f"3. You should see MUCH better results with real websites!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        # Create sample file as fallback
        sample_file = create_sample_company_websites()
        print(f"✅ Created sample file as fallback: {sample_file}")

if __name__ == "__main__":
    main()
