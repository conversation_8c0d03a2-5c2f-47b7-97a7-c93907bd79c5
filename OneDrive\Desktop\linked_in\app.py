import pandas as pd
import re
import phonenumbers
from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright, TimeoutError
import logging
import random
import time
import os
import urllib.parse

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

INPUT_FILE = r"C:\Users\<USER>\Downloads\website_urls_output.xlsx"
OUTPUT_FILE = r"C:\Users\<USER>\Downloads\website_scraped_data.xlsx"

# === Utilities ===

def validate_email(email):
    """Validate email format with relaxed rules to capture more valid data."""
    email_regex = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
    if not re.match(email_regex, email, re.IGNORECASE):
        return False
    # Relaxed invalid patterns to avoid filtering valid emails
    invalid_patterns = [
        r'\.(png|jpg|jpeg|gif|svg|pdf)$',  # File extensions
        r'@invalid\.'  # Only strict invalid domains
    ]
    return not any(re.search(pattern, email, re.IGNORECASE) for pattern in invalid_patterns)

def extract_emails(text):
    """Extract and validate unique email addresses from text."""
    emails = set(re.findall(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}", text, re.IGNORECASE))
    valid_emails = [email for email in emails if validate_email(email)]
    logging.debug(f"Extracted emails: {valid_emails}")
    return sorted(valid_emails)

def validate_phone(phone, region=None):
    """Validate phone number using phonenumbers, supporting international formats."""
    try:
        parsed = phonenumbers.parse(phone, region)
        if phonenumbers.is_valid_number_for_region(parsed, region) or phonenumbers.is_possible_number(parsed):
            return phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
        return None
    except phonenumbers.NumberParseException:
        return None

def extract_phones(text, region=None):
    """Extract and validate unique phone numbers from text."""
    phone_regex = r"(\(?\+?\d{1,4}\)?[\s.-]?\d{1,10}[\s.-]?\d{1,10})"  # Broader range for digits
    potential_phones = set(re.findall(phone_regex, text))
    valid_phones = []
    for phone in potential_phones:
        cleaned_phone = re.sub(r'[^\d+]', '', phone)
        if len(cleaned_phone) >= 8:  # Minimum 8 digits to allow more valid numbers
            formatted_phone = validate_phone(cleaned_phone, region)
            if formatted_phone:
                valid_phones.append(formatted_phone)
    logging.debug(f"Extracted phones: {valid_phones}")
    return sorted(list(set(valid_phones)))

def sanitize_filename(url):
    """Sanitize URL to create a valid file name by removing query strings and special characters."""
    parsed_url = urllib.parse.urlparse(url)
    safe_path = parsed_url.netloc + parsed_url.path
    safe_path = re.sub(r'[<>:"/\\|?*]', '_', safe_path)
    safe_path = re.sub(r'\.+', '_', safe_path)[:100]  # Limit to 100 characters, handle multiple dots
    return f"debug_{safe_path}.html" if safe_path else "debug_unknown.html"

def scrape_page_content(url, max_retries=5):
    """Scrape page content and extract emails and phones with enhanced reliability."""
    for attempt in range(max_retries):
        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                context = browser.new_context(user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
                page = context.new_page()
                page.goto(url, wait_until='networkidle', timeout=120000)  # 120-second timeout, wait for network idle
                page.wait_for_load_state('networkidle', timeout=30000)  # 30-second wait for full load
                page.wait_for_selector('body', state='visible', timeout=30000)
                page.wait_for_selector('footer, .contact, address, [class*="contact"], [id*="contact"], .email, .phone, a[href*="contact"], a[href*="about"]', state='visible', timeout=30000, strict=False)
                html = page.content()
                debug_dir = r"C:\Users\<USER>\Downloads\debug_html"
                os.makedirs(debug_dir, exist_ok=True)
                debug_file = os.path.join(debug_dir, sanitize_filename(url))
                with open(debug_file, 'w', encoding='utf-8') as f:
                    f.write(html)
                logging.info(f"Debug HTML saved to {debug_file}")

                soup = BeautifulSoup(html, 'lxml')
                tags = soup.find_all(['p', 'div', 'span', 'a', 'li', 'section', 'footer', 'header', 'address', 'contact', '[class*="contact"]', '[id*="contact"]', '[class*="email"]', '[class*="phone"]', 'td', 'th'])
                text = ' '.join(tag.get_text(separator=' ', strip=True) for tag in tags if tag.get_text(strip=True))

                # Follow contact or about links if no text
                if not text.strip():
                    contact_links = soup.find_all('a', href=re.compile(r'contact|about|info', re.IGNORECASE))
                    for link in contact_links[:3]:  # Limit to 3 links to avoid infinite loops
                        href = link.get('href', '')
                        full_url = href if href.startswith('http') else (url + href if href.startswith('/') else url.rstrip('/') + '/' + href)
                        try:
                            page.goto(full_url, wait_until='networkidle', timeout=120000)
                            page.wait_for_load_state('networkidle', timeout=30000)
                            html_contact = page.content()
                            soup_contact = BeautifulSoup(html_contact, 'lxml')
                            tags_contact = soup_contact.find_all(['p', 'div', 'span', 'a', 'address', 'td', 'th'])
                            text += ' ' + ' '.join(tag.get_text(separator=' ', strip=True) for tag in tags_contact if tag.get_text(strip=True))
                        except Exception as e:
                            logging.warning(f"Failed to scrape contact link {full_url}: {e}")
                logging.debug(f"Extracted text length: {len(text)} characters")

                emails = extract_emails(text)
                phones = extract_phones(text, region=None)

                if not emails and not phones:
                    logging.warning(f"No valid emails or phones found for {url}")
                else:
                    logging.info(f"Extracted data for {url}: Emails={emails}, Phones={phones}")

                browser.close()
                return emails, phones

        except TimeoutError:
            logging.warning(f"Timeout on attempt {attempt + 1} for {url}")
            if attempt == max_retries - 1:
                logging.error(f"Failed to scrape {url} after {max_retries} attempts")
                return [], []
        except Exception as e:
            logging.error(f"Error scraping {url}: {e}")
            return [], []
        finally:
            time.sleep(random.uniform(5, 10))  # 5-10 second delay

    return [], []

# === Main ===

def main():
    try:
        logging.info(f"Loading input file: {INPUT_FILE}")
        df = pd.read_excel(INPUT_FILE)
        df.columns = df.columns.str.strip()
        print("Columns found in Excel:", df.columns.tolist())

        if 'Website URL' not in df.columns:
            raise ValueError("Excel file must have a column named 'Website URL'")

        results = []

        for idx, row in df.iterrows():
            website_url = str(row['Website URL']).strip()
            if website_url and website_url != "N/A":
                logging.info(f"Scraping Website URL: {website_url}")
                website_emails, website_phones = scrape_page_content(website_url)
            else:
                logging.info(f"No valid Website URL for row {idx}")
                website_emails, website_phones = [], []

            results.append({
                "Website URL": website_url if website_url and website_url != "N/A" else "N/A",
                "Website Emails": ", ".join(website_emails) if website_emails else "N/A",
                "Website Phones": ", ".join(website_phones) if website_phones else "N/A"
            })

        logging.info(f"Saving results to {OUTPUT_FILE}")
        pd.DataFrame(results).to_excel(OUTPUT_FILE, index=False)
        logging.info(f"All data saved to {OUTPUT_FILE}")

    except Exception as e:
        logging.error(f"Script failed: {e}")
        raise

if __name__ == "__main__":
    main()