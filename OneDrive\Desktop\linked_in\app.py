import pandas as pd
import re
import phonenumbers
from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright
import logging
import random
import time
import urllib.parse

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

INPUT_FILE = r"C:\Users\<USER>\Downloads\website_urls_output.xlsx"
OUTPUT_FILE = r"C:\Users\<USER>\Downloads\website_scraped_data.xlsx"

# === Utilities ===

def validate_email(email):
    """Validate email format with relaxed rules to capture more valid data."""
    # More comprehensive email regex
    email_regex = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
    if not re.match(email_regex, email, re.IGNORECASE):
        return False

    # Skip obvious non-emails
    invalid_patterns = [
        r'\.(png|jpg|jpeg|gif|svg|pdf|css|js|ico)$',  # File extensions
        r'@(example|test|invalid|localhost|domain)\.',  # Test domains
        r'@.*\.(png|jpg|jpeg|gif|svg|pdf)$',  # Email ending with file extension
        r'^(no-reply|noreply|donotreply)@',  # System emails (optional to include)
    ]

    # Allow most emails, only filter obvious invalid ones
    for pattern in invalid_patterns:
        if re.search(pattern, email, re.IGNORECASE):
            return False

    return True

def extract_emails(text):
    """Extract and validate unique email addresses from text with multiple patterns."""
    email_patterns = [
        r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}",  # Standard email
        r"[a-zA-Z0-9._%+-]+\s*@\s*[a-zA-Z0-9.-]+\s*\.\s*[a-zA-Z]{2,}",  # With spaces
        r"[a-zA-Z0-9._%+-]+\[at\][a-zA-Z0-9.-]+\[dot\][a-zA-Z]{2,}",  # Obfuscated [at] [dot]
        r"[a-zA-Z0-9._%+-]+\s*\(at\)\s*[a-zA-Z0-9.-]+\s*\(dot\)\s*[a-zA-Z]{2,}",  # Obfuscated (at) (dot)
    ]

    emails = set()
    for pattern in email_patterns:
        found_emails = re.findall(pattern, text, re.IGNORECASE)
        for email in found_emails:
            # Clean up obfuscated emails
            clean_email = email.replace('[at]', '@').replace('[dot]', '.').replace('(at)', '@').replace('(dot)', '.')
            clean_email = re.sub(r'\s+', '', clean_email)  # Remove spaces
            emails.add(clean_email.lower())

    valid_emails = [email for email in emails if validate_email(email)]
    logging.debug(f"Extracted emails: {valid_emails}")
    return sorted(valid_emails)

def validate_phone(phone, region=None):
    """Validate phone number using phonenumbers, supporting international formats."""
    try:
        # Try parsing with different regions
        regions_to_try = [region, 'US', 'GB', 'IN', 'CA', 'AU', None]

        for reg in regions_to_try:
            try:
                parsed = phonenumbers.parse(phone, reg)
                if phonenumbers.is_valid_number(parsed) or phonenumbers.is_possible_number(parsed):
                    return phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
            except phonenumbers.NumberParseException:
                continue
        return None
    except Exception:
        return None

def extract_phones(text, region=None):
    """Extract and validate unique phone numbers from text with multiple patterns."""
    phone_patterns = [
        r"(\+?\d{1,4}[\s.-]?\(?\d{3,4}\)?[\s.-]?\d{3,4}[\s.-]?\d{3,4})",  # International format
        r"(\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4})",  # US format
        r"(\+?\d{1,4}[\s.-]?\d{2,4}[\s.-]?\d{2,4}[\s.-]?\d{2,4}[\s.-]?\d{2,4})",  # General international
        r"(\d{3}[\s.-]?\d{3}[\s.-]?\d{4})",  # Simple US format
        r"(\+?\d{10,15})",  # Continuous digits
        r"(\d{10})",  # 10 digit US numbers
        r"(\+\d{1,4}\s?\d{1,4}\s?\d{1,4}\s?\d{1,4}\s?\d{1,4})",  # Spaced international
    ]

    potential_phones = set()
    for pattern in phone_patterns:
        found_phones = re.findall(pattern, text)
        potential_phones.update(found_phones)

    valid_phones = []
    for phone in potential_phones:
        # Clean phone number
        cleaned_phone = re.sub(r'[^\d+]', '', phone)

        # Skip if too short or too long
        if len(cleaned_phone) < 7 or len(cleaned_phone) > 15:
            continue

        # Skip obvious non-phone patterns
        if re.match(r'^(0+|1+|2+|3+|4+|5+|6+|7+|8+|9+)$', cleaned_phone):
            continue

        formatted_phone = validate_phone(cleaned_phone, region)
        if formatted_phone:
            valid_phones.append(formatted_phone)
        elif len(cleaned_phone) >= 10:  # Keep long numbers even if not validated
            valid_phones.append(cleaned_phone)

    logging.debug(f"Extracted phones: {valid_phones}")
    return sorted(list(set(valid_phones)))

def sanitize_filename(url):
    """Sanitize URL to create a valid file name by removing query strings and special characters."""
    parsed_url = urllib.parse.urlparse(url)
    safe_path = parsed_url.netloc + parsed_url.path
    safe_path = re.sub(r'[<>:"/\\|?*]', '_', safe_path)
    safe_path = re.sub(r'\.+', '_', safe_path)[:100]  # Limit to 100 characters, handle multiple dots
    return f"debug_{safe_path}.html" if safe_path else "debug_unknown.html"

def normalize_url(url):
    """Normalize URL by adding protocol if missing and cleaning up."""
    if not url or url.strip().lower() in ['n/a', 'na', '', 'none', 'null']:
        return None

    url = url.strip()

    # Add protocol if missing
    if not url.startswith(('http://', 'https://')):
        # Try https first, then http if it fails
        url = 'https://' + url

    # Remove trailing slashes and clean up
    url = url.rstrip('/')

    return url

def scrape_page_content(url, max_retries=3):
    """Scrape page content and extract emails and phones with enhanced reliability."""
    all_emails = set()
    all_phones = set()

    for attempt in range(max_retries):
        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                context = browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    viewport={'width': 1920, 'height': 1080}
                )
                page = context.new_page()

                # Set longer timeout and better error handling
                page.set_default_timeout(60000)  # 60 seconds

                # Navigate to the main page
                try:
                    response = page.goto(url, wait_until='domcontentloaded', timeout=60000)
                    if response and response.status >= 400:
                        logging.warning(f"HTTP {response.status} for {url}")
                except Exception as e:
                    logging.warning(f"Failed to load {url}: {e}")
                    continue

                # Wait for page to load
                try:
                    page.wait_for_load_state('networkidle', timeout=15000)
                except:
                    pass  # Continue even if networkidle fails

                # Get main page content
                html = page.content()

                # Parse main page
                soup = BeautifulSoup(html, 'lxml')

                # Extract text from various elements
                text_elements = soup.find_all([
                    'p', 'div', 'span', 'a', 'li', 'section', 'footer', 'header',
                    'address', 'td', 'th', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
                ])

                # Also look for specific contact-related elements
                contact_elements = soup.find_all(attrs={
                    'class': re.compile(r'contact|email|phone|tel|mail', re.IGNORECASE)
                }) + soup.find_all(attrs={
                    'id': re.compile(r'contact|email|phone|tel|mail', re.IGNORECASE)
                })

                all_elements = text_elements + contact_elements
                text = ' '.join(tag.get_text(separator=' ', strip=True) for tag in all_elements if tag.get_text(strip=True))

                # Extract from href attributes (mailto: and tel: links)
                mailto_links = soup.find_all('a', href=re.compile(r'^mailto:', re.IGNORECASE))
                tel_links = soup.find_all('a', href=re.compile(r'^tel:', re.IGNORECASE))

                for link in mailto_links:
                    href = link.get('href', '')
                    email = href.replace('mailto:', '').split('?')[0]  # Remove query params
                    if validate_email(email):
                        all_emails.add(email.lower())

                for link in tel_links:
                    href = link.get('href', '')
                    phone = href.replace('tel:', '').replace('+', '').replace('-', '').replace(' ', '')
                    if len(phone) >= 7:
                        all_phones.add(phone)

                # Extract from main page text
                page_emails = extract_emails(text)
                page_phones = extract_phones(text)
                all_emails.update(email.lower() for email in page_emails)
                all_phones.update(page_phones)

                # Try to find and scrape contact/about pages
                contact_urls = set()
                contact_patterns = [
                    r'contact', r'about', r'info', r'reach', r'connect',
                    r'support', r'help', r'team', r'staff'
                ]

                for pattern in contact_patterns:
                    contact_links = soup.find_all('a', href=re.compile(pattern, re.IGNORECASE))
                    for link in contact_links[:2]:  # Limit to 2 per pattern
                        href = link.get('href', '')
                        if href:
                            if href.startswith('http'):
                                contact_urls.add(href)
                            elif href.startswith('/'):
                                contact_urls.add(url.rstrip('/') + href)
                            elif not href.startswith('#') and not href.startswith('mailto:') and not href.startswith('tel:'):
                                contact_urls.add(url.rstrip('/') + '/' + href.lstrip('/'))

                # Scrape contact pages
                for contact_url in list(contact_urls)[:5]:  # Limit to 5 contact pages
                    try:
                        logging.info(f"Scraping contact page: {contact_url}")
                        page.goto(contact_url, wait_until='domcontentloaded', timeout=30000)
                        page.wait_for_load_state('networkidle', timeout=10000)

                        contact_html = page.content()
                        contact_soup = BeautifulSoup(contact_html, 'lxml')

                        contact_text_elements = contact_soup.find_all([
                            'p', 'div', 'span', 'a', 'address', 'td', 'th', 'li'
                        ])
                        contact_text = ' '.join(tag.get_text(separator=' ', strip=True)
                                              for tag in contact_text_elements if tag.get_text(strip=True))

                        # Extract from contact page
                        contact_emails = extract_emails(contact_text)
                        contact_phones = extract_phones(contact_text)
                        all_emails.update(email.lower() for email in contact_emails)
                        all_phones.update(contact_phones)

                        # Extract from contact page mailto/tel links
                        contact_mailto = contact_soup.find_all('a', href=re.compile(r'^mailto:', re.IGNORECASE))
                        contact_tel = contact_soup.find_all('a', href=re.compile(r'^tel:', re.IGNORECASE))

                        for link in contact_mailto:
                            href = link.get('href', '')
                            email = href.replace('mailto:', '').split('?')[0]
                            if validate_email(email):
                                all_emails.add(email.lower())

                        for link in contact_tel:
                            href = link.get('href', '')
                            phone = href.replace('tel:', '').replace('+', '').replace('-', '').replace(' ', '')
                            if len(phone) >= 7:
                                all_phones.add(phone)

                    except Exception as e:
                        logging.warning(f"Failed to scrape contact page {contact_url}: {e}")
                        continue

                browser.close()

                final_emails = sorted(list(all_emails))
                final_phones = sorted(list(all_phones))

                if final_emails or final_phones:
                    logging.info(f"Successfully extracted from {url}: Emails={final_emails}, Phones={final_phones}")
                    return final_emails, final_phones
                else:
                    logging.warning(f"No contact info found for {url}")
                    if attempt < max_retries - 1:
                        continue
                    return [], []

        except Exception as e:
            logging.error(f"Error scraping {url} on attempt {attempt + 1}: {e}")
            if attempt == max_retries - 1:
                return [], []
        finally:
            time.sleep(random.uniform(2, 5))  # Shorter delay

    return [], []

# === Main ===

def main():
    try:
        logging.info(f"Loading input file: {INPUT_FILE}")
        df = pd.read_excel(INPUT_FILE)
        df.columns = df.columns.str.strip()
        print("Columns found in Excel:", df.columns.tolist())

        if 'Website URL' not in df.columns:
            raise ValueError("Excel file must have a column named 'Website URL'")

        results = []
        total_rows = len(df)
        successful_scrapes = 0

        for idx, row in df.iterrows():
            print(f"\nProcessing {idx + 1}/{total_rows}: ", end="")

            website_url = str(row['Website URL']).strip()
            normalized_url = normalize_url(website_url)

            if normalized_url:
                print(f"Scraping {normalized_url}")
                logging.info(f"Scraping Website URL: {normalized_url}")

                try:
                    website_emails, website_phones = scrape_page_content(normalized_url)

                    if website_emails or website_phones:
                        successful_scrapes += 1
                        print(f"✓ Found: {len(website_emails)} emails, {len(website_phones)} phones")
                    else:
                        print("✗ No contact info found")

                except Exception as e:
                    print(f"✗ Error: {str(e)[:50]}...")
                    logging.error(f"Error scraping {normalized_url}: {e}")
                    website_emails, website_phones = [], []
            else:
                print("✗ Invalid URL")
                logging.info(f"No valid Website URL for row {idx}")
                website_emails, website_phones = [], []

            # Simple output with only 3 columns
            results.append({
                "Website URL": website_url,
                "Website Emails": ", ".join(website_emails) if website_emails else "N/A",
                "Website Phones": ", ".join(website_phones) if website_phones else "N/A"
            })

        # Print summary
        print(f"\n{'='*50}")
        print(f"SCRAPING SUMMARY")
        print(f"{'='*50}")
        print(f"Total URLs processed: {total_rows}")
        print(f"Successful scrapes: {successful_scrapes}")
        print(f"Success rate: {(successful_scrapes/total_rows)*100:.1f}%")

        logging.info(f"Saving results to {OUTPUT_FILE}")
        result_df = pd.DataFrame(results)
        result_df.to_excel(OUTPUT_FILE, index=False)
        logging.info(f"All data saved to {OUTPUT_FILE}")

        print(f"\nResults saved to: {OUTPUT_FILE}")

    except Exception as e:
        logging.error(f"Script failed: {e}")
        raise

if __name__ == "__main__":
    main()